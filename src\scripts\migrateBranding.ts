import mongoose from 'mongoose';
import { Branding } from '../models/Branding';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from the root directory
dotenv.config({ path: path.join(__dirname, '../../.env') });

const migrateBranding = async () => {
  try {
    console.log('Starting branding migration...');
    
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is not set');
    }
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Check if branding document already exists
    const existingBranding = await Branding.findOne();
    
    if (existingBranding) {
      console.log('Branding document already exists. Updating with new fields...');
      
      // Update existing document with new fields (keeping existing values)
      await Branding.findByIdAndUpdate(existingBranding._id, {
        $set: {
          // Only set fields that don't exist
          'home.aboutTitle': existingBranding.home?.aboutTitle || 'About Goldie Locs',
          'home.aboutText': existingBranding.home?.aboutText || 'With years of experience in natural hair care and loc maintenance, Tina provides personalized services to help you achieve and maintain beautiful, healthy locs.',
          'services.pageDescription': existingBranding.services?.pageDescription || 'From starter locs to maintenance and styling, we provide comprehensive care for your natural hair journey.',
          'services.serviceLocMaintenance': existingBranding.services?.serviceLocMaintenance || 'Loc Maintenance',
          'services.serviceLocMaintenanceDesc': existingBranding.services?.serviceLocMaintenanceDesc || 'Professional maintenance to keep your locs healthy and looking their best',
          'services.serviceStarterLocs': existingBranding.services?.serviceStarterLocs || 'Starter Locs',
          'services.serviceStarterLocsDesc': existingBranding.services?.serviceStarterLocsDesc || 'Begin your loc journey with expert guidance and professional techniques',
          'services.serviceLocStyling': existingBranding.services?.serviceLocStyling || 'Loc Styling',
          'services.serviceLocStylingDesc': existingBranding.services?.serviceLocStylingDesc || 'Creative styling options to showcase your locs for any occasion',
          'services.serviceNaturalHairCare': existingBranding.services?.serviceNaturalHairCare || 'Natural Hair Care',
          'services.serviceNaturalHairCareDesc': existingBranding.services?.serviceNaturalHairCareDesc || 'Comprehensive care for natural hair health and growth',
          'shop.pageDescription': existingBranding.shop?.pageDescription || 'Discover our curated collection of premium hair care products designed specifically for locs and natural hair.',
          'consultation.pageDescription': existingBranding.consultation?.pageDescription || 'Schedule a one-on-one consultation to discuss your hair goals, assess your hair type, and create a customized care plan.',
          
          // Dashboard content
          dashboard: existingBranding.dashboard || {
            welcomeMessage: 'Welcome back to your hair care journey!',
            overviewTitle: 'Your Hair Care Overview',
            appointmentsTitle: 'Your Appointments',
            ordersTitle: 'Your Orders',
            favoritesTitle: 'Your Favorites',
            profileTitle: 'Profile Settings',
            nextAppointment: 'Your Next Appointment',
            recentOrders: 'Recent Orders',
            loyaltyTitle: 'Loyalty Rewards'
          },
          
          // Buttons
          buttons: existingBranding.buttons || {
            bookNow: 'Book Now',
            shopNow: 'Shop Now',
            learnMore: 'Learn More',
            viewAll: 'View All',
            continueShopping: 'Continue Shopping',
            proceedToCheckout: 'Proceed to Checkout',
            addToCart: 'Add to Cart',
            scheduleConsultation: 'Schedule Consultation',
            writeReview: 'Write a Review'
          },
          
          // Navigation
          navigation: existingBranding.navigation || {
            home: 'Home',
            services: 'Services',
            shop: 'Shop',
            consultation: 'Book Consultation',
            login: 'Login',
            signup: 'Sign Up',
            dashboard: 'Dashboard'
          },
          
          // Testimonials
          testimonials: existingBranding.testimonials || {
            title: 'What Our Clients Say',
            subtitle: 'Real experiences from our satisfied customers'
          },
          
          // Reviews
          reviews: existingBranding.reviews || {
            title: 'Customer Reviews'
          },
          
          // Messages
          messages: existingBranding.messages || {
            loading: 'Loading...',
            error: 'Something went wrong. Please try again.',
            notFound: 'Page not found',
            comingSoon: 'Coming soon!',
            cartShipping: 'Free shipping on orders over $50'
          },
          
          // Business information
          business: existingBranding.business || {
            name: 'MicroLocs',
            tagline: 'Professional Hair Care Services',
            description: 'Professional locs and natural hair care services. Specializing in micro locs, traditional locs, and natural hair maintenance.',
            phone: '(*************',
            email: '<EMAIL>',
            address: {
              street: '123 Beauty Street',
              city: 'Atlanta',
              state: 'GA',
              zip: '30309',
              full: '123 Beauty Street, Atlanta, GA 30309'
            },
            social: {
              instagram: 'https://instagram.com/goldielocs',
              facebook: 'https://facebook.com/goldielocs',
              twitter: 'https://twitter.com/goldielocs'
            },
            hours: {
              monday: '9:00 AM - 6:00 PM',
              tuesday: '9:00 AM - 6:00 PM',
              wednesday: '9:00 AM - 6:00 PM',
              thursday: '9:00 AM - 6:00 PM',
              friday: '9:00 AM - 6:00 PM',
              saturday: '9:00 AM - 4:00 PM',
              sunday: 'Closed'
            }
          },
          
          // Theme settings
          theme: existingBranding.theme || {
            colors: {
              primary: '#008000',
              secondary: '#f3d016',
              accent: '#006600',
              background: '#ffffff',
              text: '#000000',
              textSecondary: '#666666'
            },
            fonts: {
              primary: 'Inter, system-ui, sans-serif',
              secondary: 'Inter, system-ui, sans-serif',
              heading: 'Inter, system-ui, sans-serif'
            }
          },
          
          // Site settings
          site: existingBranding.site || {
            seo: {
              title: 'Goldie Locs By Tina - Professional Loc Services',
              description: 'Professional locs and natural hair care services in Atlanta, GA. Specializing in micro locs, traditional locs, and natural hair maintenance.',
              keywords: 'locs, dreadlocks, natural hair, hair care, Atlanta, micro locs, loc maintenance'
            },
            features: {
              onlineBooking: true,
              ecommerce: true,
              loyaltyProgram: true,
              giftCards: true,
              reviews: true,
              blog: false
            }
          }
        }
      });
      
      console.log('Updated existing branding document with new fields');
    } else {
      console.log('Creating new branding document...');
      
      // Create new branding document with all default values
      await Branding.create({});
      
      console.log('Created new branding document with default values');
    }
    
    console.log('Branding migration completed successfully!');
    
  } catch (error) {
    console.error('Branding migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run migration if this file is executed directly
if (require.main === module) {
  migrateBranding();
}

export default migrateBranding;
