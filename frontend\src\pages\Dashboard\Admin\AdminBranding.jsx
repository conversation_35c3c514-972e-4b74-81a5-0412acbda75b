import { useState, useEffect } from 'react'
import {
  FiEdit, FiImage, FiPackage, FiCalendar, FiShoppingBag, FiUsers,
  FiUserCheck, FiBarChart, FiSettings, FiHome, FiPlus, FiRefreshCw, FiSave
} from 'react-icons/fi'
import BrandingModal from '../../../components/Modals/BrandingModal'
import { adminService } from '../../../services'
import { brandingService } from '../../../services/brandingService'
import { useBranding } from '../../../contexts/BrandingContext'

const AdminBranding = ({
  showToast,
  branding
}) => {
  const { refreshBranding } = useBranding()
  const [brandingData, setBrandingData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [activeBrandingSection, setActiveBrandingSection] = useState('global')
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  useEffect(() => {
    loadBrandingData()
  }, [])

  const loadBrandingData = async () => {
    try {
      setLoading(true)
      console.log('Loading complete branding data...')
      const response = await brandingService.getCompleteBranding()
      console.log('Branding response:', response)

      if (response.success && response.data) {
        // Structure the data for editing
        const { branding: brandingContent, business, theme, site } = response.data
        console.log('Branding content:', brandingContent)
        console.log('Business data:', business)
        console.log('Theme data:', theme)

        setBrandingData({
          // Business Information
          global: {
            siteName: brandingContent?.global?.siteName || business?.name || '',
            tagline: brandingContent?.global?.tagline || business?.tagline || '',
            phone: brandingContent?.global?.phone || business?.phone || '',
            email: brandingContent?.global?.email || business?.email || '',
            address: brandingContent?.global?.address || business?.address?.full || '',
            logo: brandingContent?.global?.logo || '',
            favicon: brandingContent?.global?.favicon || ''
          },

          // Social Media (from branding.global in backend)
          social: {
            instagram: brandingContent?.global?.instagram || business?.social?.instagram || '',
            facebook: brandingContent?.global?.facebook || business?.social?.facebook || '',
            twitter: brandingContent?.global?.twitter || business?.social?.twitter || '',
            youtube: brandingContent?.global?.youtube || ''
          },

          // Theme Settings
          theme: {
            primaryColor: theme?.colors?.primary || '#008000',
            secondaryColor: theme?.colors?.secondary || '#f3d016',
            accentColor: theme?.colors?.accent || '#006600',
            backgroundColor: theme?.colors?.background || '#ffffff',
            textColor: theme?.colors?.text || '#000000',
            textSecondaryColor: theme?.colors?.textSecondary || '#666666'
          },

          // Home Page Content (from flat branding response)
          home: {
            heroTitle: brandingContent?.heroTitle || '',
            heroSubtitle: brandingContent?.heroSubtitle || '',
            heroImage: brandingContent?.heroImage || '',
            aboutTitle: brandingContent?.aboutTitle || '',
            aboutText: brandingContent?.aboutText || '',
            featuredServices: brandingContent?.featuredServices || [],
            testimonialHeading: brandingContent?.testimonialHeading || ''
          },

          // Services Content (from flat branding response)
          services: {
            servicesTitle: brandingContent?.servicesTitle || '',
            servicesSubtitle: brandingContent?.servicesSubtitle || '',
            servicesDescription: brandingContent?.servicesDescription || '',
            serviceLocMaintenance: brandingContent?.serviceLocMaintenance || '',
            serviceLocMaintenanceDesc: brandingContent?.serviceLocMaintenanceDesc || '',
            serviceStarterLocs: brandingContent?.serviceStarterLocs || '',
            serviceStarterLocsDesc: brandingContent?.serviceStarterLocsDesc || '',
            serviceLocStyling: brandingContent?.serviceLocStyling || '',
            serviceLocStylingDesc: brandingContent?.serviceLocStylingDesc || '',
            serviceNaturalHairCare: brandingContent?.serviceNaturalHairCare || '',
            serviceNaturalHairCareDesc: brandingContent?.serviceNaturalHairCareDesc || '',
            serviceCategories: brandingContent?.serviceCategories || []
          },

          // Shop Content (from flat branding response)
          shop: {
            shopTitle: brandingContent?.shopTitle || '',
            shopSubtitle: brandingContent?.shopSubtitle || '',
            shopDescription: brandingContent?.shopDescription || '',
            shopFeaturedTitle: brandingContent?.shopFeaturedTitle || ''
          },

          // Consultation Content (from flat branding response)
          consultation: {
            consultationTitle: brandingContent?.consultationTitle || '',
            consultationSubtitle: brandingContent?.consultationSubtitle || '',
            consultationDescription: brandingContent?.consultationDescription || '',
            consultationFormTitle: brandingContent?.consultationFormTitle || '',
            consultationFormSubtitle: brandingContent?.consultationFormSubtitle || ''
          },

          // Authentication Content (from flat branding response)
          auth: {
            loginTitle: brandingContent?.loginTitle || '',
            loginSubtitle: brandingContent?.loginSubtitle || '',
            signupTitle: brandingContent?.signupTitle || '',
            signupSubtitle: brandingContent?.signupSubtitle || ''
          },

          // Cart Content (from flat branding response)
          cart: {
            cartTitle: brandingContent?.cartTitle || '',
            emptyCartMessage: brandingContent?.cartEmptyMessage || '',
            cartShippingMessage: brandingContent?.cartShippingMessage || ''
          },

          // Dashboard Content (from flat branding response)
          dashboard: {
            dashboardWelcome: brandingContent?.dashboardWelcome || '',
            dashboardOverviewTitle: brandingContent?.dashboardOverviewTitle || '',
            dashboardAppointmentsTitle: brandingContent?.dashboardAppointmentsTitle || '',
            dashboardOrdersTitle: brandingContent?.dashboardOrdersTitle || '',
            dashboardFavoritesTitle: brandingContent?.dashboardFavoritesTitle || '',
            dashboardProfileTitle: brandingContent?.dashboardProfileTitle || '',
            dashboardNextAppointment: brandingContent?.dashboardNextAppointment || '',
            dashboardRecentOrders: brandingContent?.dashboardRecentOrders || '',
            dashboardLoyaltyTitle: brandingContent?.dashboardLoyaltyTitle || ''
          },

          // Buttons (from flat branding response)
          buttons: {
            bookNowButton: brandingContent?.bookNowButton || '',
            shopNowButton: brandingContent?.shopNowButton || '',
            learnMoreButton: brandingContent?.learnMoreButton || '',
            viewAllButton: brandingContent?.viewAllButton || '',
            continueShoppingButton: brandingContent?.continueShoppingButton || '',
            proceedToCheckoutButton: brandingContent?.proceedToCheckoutButton || '',
            addToCartButton: brandingContent?.addToCartButton || '',
            scheduleConsultationButton: brandingContent?.scheduleConsultationButton || '',
            writeReviewButton: brandingContent?.writeReviewButton || ''
          },

          // Navigation (from flat branding response)
          navigation: {
            navHome: brandingContent?.navHome || '',
            navServices: brandingContent?.navServices || '',
            navShop: brandingContent?.navShop || '',
            navConsultation: brandingContent?.navConsultation || '',
            navLogin: brandingContent?.navLogin || '',
            navSignup: brandingContent?.navSignup || '',
            navDashboard: brandingContent?.navDashboard || ''
          },

          // Testimonials (from flat branding response)
          testimonials: {
            testimonialsTitle: brandingContent?.testimonialsTitle || '',
            testimonialsSubtitle: brandingContent?.testimonialsSubtitle || ''
          },

          // Reviews (from flat branding response)
          reviews: {
            reviewsTitle: brandingContent?.reviewsTitle || ''
          },

          // Messages (from flat branding response)
          messages: {
            loadingMessage: brandingContent?.loadingMessage || '',
            errorMessage: brandingContent?.errorMessage || '',
            notFoundMessage: brandingContent?.notFoundMessage || '',
            comingSoonMessage: brandingContent?.comingSoonMessage || '',
            cartShippingMessage: brandingContent?.cartShippingMessage || ''
          },

          // Footer Content (from flat branding response)
          footer: {
            footerCopyright: brandingContent?.footerCopyright || ''
          },

          // Business Information (from business object)
          business: business || {},

          // Theme Settings (from theme object)
          theme: theme || {},

          // Site Settings (from site object)
          site: site || {}
        })

        console.log('Structured branding data set successfully')
      } else {
        throw new Error('Failed to load complete branding data')
      }
    } catch (error) {
      console.error('Error loading branding data:', error)
      showToast('Error loading branding data', 'error')
      setBrandingData({})
    } finally {
      setLoading(false)
    }
  }

  const handleSaveBranding = async (formData) => {
    try {
      setSaving(true)

      // Transform the form data to match the backend Branding model structure
      const brandingPayload = {
        global: {
          siteName: formData.global?.siteName || '',
          tagline: formData.global?.tagline || '',
          phone: formData.global?.phone || '',
          email: formData.global?.email || '',
          address: formData.global?.address || '',
          logo: formData.global?.logo || '',
          // Social media fields go directly in global for backend model
          instagram: formData.social?.instagram || '',
          facebook: formData.social?.facebook || '',
          twitter: formData.social?.twitter || '',
          youtube: formData.social?.youtube || ''
        },
        home: {
          heroTitle: formData.home?.heroTitle || '',
          heroSubtitle: formData.home?.heroSubtitle || '',
          heroImage: formData.home?.heroImage || '',
          featuredServices: formData.home?.featuredServices || [],
          testimonialHeading: formData.home?.testimonialHeading || ''
        },
        services: {
          pageTitle: formData.services?.servicesTitle || '',
          pageSubtitle: formData.services?.servicesSubtitle || '',
          serviceCategories: formData.services?.serviceCategories || []
        },
        shop: {
          pageTitle: formData.shop?.shopTitle || '',
          pageSubtitle: formData.shop?.shopSubtitle || '',
          featuredCollectionTitle: formData.shop?.shopFeaturedTitle || ''
        },
        consultation: {
          pageTitle: formData.consultation?.consultationTitle || '',
          pageSubtitle: formData.consultation?.consultationSubtitle || '',
          formTitle: formData.consultation?.consultationFormTitle || '',
          formSubtitle: formData.consultation?.consultationFormSubtitle || ''
        },
        login: {
          pageTitle: formData.auth?.loginTitle || '',
          pageSubtitle: formData.auth?.loginSubtitle || ''
        },
        signup: {
          pageTitle: formData.auth?.signupTitle || '',
          pageSubtitle: formData.auth?.signupSubtitle || '',
          agreeTermsText: formData.auth?.agreeTermsText || '',
          termsLinkText: formData.auth?.termsLinkText || '',
          andText: formData.auth?.andText || '',
          privacyLinkText: formData.auth?.privacyLinkText || ''
        },
        cart: {
          pageTitle: formData.cart?.cartTitle || '',
          emptyCartMessage: formData.cart?.emptyCartMessage || '',
          freeShippingThreshold: formData.cart?.freeShippingThreshold || '',
          shippingCalculated: formData.cart?.shippingCalculated || ''
        },
        productDetail: {
          addToCartButton: formData.productDetail?.addToCartButton || '',
          quantityLabel: formData.productDetail?.quantityLabel || '',
          overviewTab: formData.productDetail?.overviewTab || '',
          ingredientsTab: formData.productDetail?.ingredientsTab || '',
          reviewsTab: formData.productDetail?.reviewsTab || ''
        },
        footer: {
          description: formData.footer?.footerDescription || '',
          copyrightText: formData.footer?.footerCopyright || '',
          quickLinks: formData.footer?.quickLinks || [
            { name: 'Privacy Policy', url: '/privacy' },
            { name: 'Terms of Service', url: '/terms' }
          ]
        }
      }

      console.log('Sending branding payload:', brandingPayload)
      const response = await adminService.updateBranding(brandingPayload)
      console.log('Branding update response:', response)

      if (response.success) {
        setBrandingData(formData)
        setHasUnsavedChanges(false)
        showToast('Branding updated successfully!', 'success')
        // Refresh branding context to apply changes immediately
        await refreshBranding()
      } else {
        throw new Error(response.message || 'Failed to update branding')
      }
    } catch (error) {
      console.error('Error saving branding:', error)
      showToast(`Error saving branding: ${error.message}`, 'error')
    } finally {
      setSaving(false)
    }
  }

  const handleSaveChanges = async () => {
    if (!hasUnsavedChanges || !brandingData) return

    try {
      setSaving(true)

      // Transform the form data to match the backend Branding model structure
      const brandingPayload = {
        global: {
          siteName: brandingData.global?.siteName || '',
          tagline: brandingData.global?.tagline || '',
          phone: brandingData.global?.phone || '',
          email: brandingData.global?.email || '',
          address: brandingData.global?.address || '',
          logo: brandingData.global?.logo || '',
          // Social media fields go directly in global for backend model
          instagram: brandingData.social?.instagram || '',
          facebook: brandingData.social?.facebook || '',
          twitter: brandingData.social?.twitter || '',
          youtube: brandingData.social?.youtube || ''
        },
        home: {
          heroTitle: brandingData.home?.heroTitle || '',
          heroSubtitle: brandingData.home?.heroSubtitle || '',
          heroImage: brandingData.home?.heroImage || '',
          aboutTitle: brandingData.home?.aboutTitle || '',
          aboutText: brandingData.home?.aboutText || '',
          featuredServices: brandingData.home?.featuredServices || [],
          testimonialHeading: brandingData.home?.testimonialHeading || ''
        },
        services: {
          pageTitle: brandingData.services?.servicesTitle || '',
          pageSubtitle: brandingData.services?.servicesSubtitle || '',
          pageDescription: brandingData.services?.servicesDescription || '',
          serviceCategories: brandingData.services?.serviceCategories || [],
          serviceLocMaintenance: brandingData.services?.serviceLocMaintenance || '',
          serviceLocMaintenanceDesc: brandingData.services?.serviceLocMaintenanceDesc || '',
          serviceStarterLocs: brandingData.services?.serviceStarterLocs || '',
          serviceStarterLocsDesc: brandingData.services?.serviceStarterLocsDesc || '',
          serviceLocStyling: brandingData.services?.serviceLocStyling || '',
          serviceLocStylingDesc: brandingData.services?.serviceLocStylingDesc || '',
          serviceNaturalHairCare: brandingData.services?.serviceNaturalHairCare || '',
          serviceNaturalHairCareDesc: brandingData.services?.serviceNaturalHairCareDesc || ''
        },
        shop: {
          pageTitle: brandingData.shop?.shopTitle || '',
          pageSubtitle: brandingData.shop?.shopSubtitle || '',
          pageDescription: brandingData.shop?.shopDescription || '',
          featuredCollectionTitle: brandingData.shop?.shopFeaturedTitle || ''
        },
        consultation: {
          pageTitle: brandingData.consultation?.consultationTitle || '',
          pageSubtitle: brandingData.consultation?.consultationSubtitle || '',
          pageDescription: brandingData.consultation?.consultationDescription || '',
          formTitle: brandingData.consultation?.consultationFormTitle || '',
          formSubtitle: brandingData.consultation?.consultationFormSubtitle || ''
        },
        login: {
          pageTitle: brandingData.auth?.loginTitle || '',
          pageSubtitle: brandingData.auth?.loginSubtitle || ''
        },
        signup: {
          pageTitle: brandingData.auth?.signupTitle || '',
          pageSubtitle: brandingData.auth?.signupSubtitle || '',
          agreeTermsText: brandingData.auth?.agreeTermsText || '',
          termsLinkText: brandingData.auth?.termsLinkText || '',
          andText: brandingData.auth?.andText || '',
          privacyLinkText: brandingData.auth?.privacyLinkText || ''
        },
        cart: {
          pageTitle: brandingData.cart?.cartTitle || '',
          emptyCartMessage: brandingData.cart?.emptyCartMessage || '',
          freeShippingThreshold: brandingData.cart?.freeShippingThreshold || '',
          shippingCalculated: brandingData.cart?.shippingCalculated || ''
        },
        productDetail: {
          addToCartButton: brandingData.productDetail?.addToCartButton || '',
          quantityLabel: brandingData.productDetail?.quantityLabel || '',
          overviewTab: brandingData.productDetail?.overviewTab || '',
          ingredientsTab: brandingData.productDetail?.ingredientsTab || '',
          reviewsTab: brandingData.productDetail?.reviewsTab || ''
        },
        footer: {
          description: brandingData.footer?.footerDescription || '',
          copyrightText: brandingData.footer?.footerCopyright || '',
          quickLinks: brandingData.footer?.quickLinks || [
            { name: 'Privacy Policy', url: '/privacy' },
            { name: 'Terms of Service', url: '/terms' }
          ]
        },
        // Dashboard content
        dashboard: {
          welcomeMessage: brandingData.dashboard?.dashboardWelcome || '',
          overviewTitle: brandingData.dashboard?.dashboardOverviewTitle || '',
          appointmentsTitle: brandingData.dashboard?.dashboardAppointmentsTitle || '',
          ordersTitle: brandingData.dashboard?.dashboardOrdersTitle || '',
          favoritesTitle: brandingData.dashboard?.dashboardFavoritesTitle || '',
          profileTitle: brandingData.dashboard?.dashboardProfileTitle || '',
          nextAppointment: brandingData.dashboard?.dashboardNextAppointment || '',
          recentOrders: brandingData.dashboard?.dashboardRecentOrders || '',
          loyaltyTitle: brandingData.dashboard?.dashboardLoyaltyTitle || ''
        },
        // Buttons
        buttons: {
          bookNow: brandingData.buttons?.bookNowButton || '',
          shopNow: brandingData.buttons?.shopNowButton || '',
          learnMore: brandingData.buttons?.learnMoreButton || '',
          viewAll: brandingData.buttons?.viewAllButton || '',
          continueShopping: brandingData.buttons?.continueShoppingButton || '',
          proceedToCheckout: brandingData.buttons?.proceedToCheckoutButton || '',
          addToCart: brandingData.buttons?.addToCartButton || '',
          scheduleConsultation: brandingData.buttons?.scheduleConsultationButton || '',
          writeReview: brandingData.buttons?.writeReviewButton || ''
        },
        // Navigation
        navigation: {
          home: brandingData.navigation?.navHome || '',
          services: brandingData.navigation?.navServices || '',
          shop: brandingData.navigation?.navShop || '',
          consultation: brandingData.navigation?.navConsultation || '',
          login: brandingData.navigation?.navLogin || '',
          signup: brandingData.navigation?.navSignup || '',
          dashboard: brandingData.navigation?.navDashboard || ''
        },
        // Testimonials
        testimonials: {
          title: brandingData.testimonials?.testimonialsTitle || '',
          subtitle: brandingData.testimonials?.testimonialsSubtitle || ''
        },
        // Reviews
        reviews: {
          title: brandingData.reviews?.reviewsTitle || ''
        },
        // Messages
        messages: {
          loading: brandingData.messages?.loadingMessage || '',
          error: brandingData.messages?.errorMessage || '',
          notFound: brandingData.messages?.notFoundMessage || '',
          comingSoon: brandingData.messages?.comingSoonMessage || '',
          cartShipping: brandingData.messages?.cartShippingMessage || ''
        },
        // Business information
        business: {
          name: brandingData.business?.name || '',
          tagline: brandingData.business?.tagline || '',
          description: brandingData.business?.description || '',
          phone: brandingData.business?.phone || '',
          email: brandingData.business?.email || '',
          address: brandingData.business?.address || {},
          social: brandingData.business?.social || {},
          hours: brandingData.business?.hours || {}
        },
        // Theme settings
        theme: {
          colors: brandingData.theme?.colors || {},
          fonts: brandingData.theme?.fonts || {}
        },
        // Site settings
        site: {
          seo: brandingData.site?.seo || {},
          features: brandingData.site?.features || {}
        }
      }

      console.log('Saving branding changes:', brandingPayload)
      const response = await adminService.updateBranding(brandingPayload)
      console.log('Branding save response:', response)

      if (response.success) {
        setHasUnsavedChanges(false)
        showToast('Branding changes saved successfully!', 'success')
        // Refresh branding context to apply changes immediately
        await refreshBranding()
      } else {
        throw new Error(response.message || 'Failed to save branding changes')
      }
    } catch (error) {
      console.error('Error saving branding changes:', error)
      showToast(`Error saving changes: ${error.message}`, 'error')
    } finally {
      setSaving(false)
    }
  }

  const renderInputField = (label, value, onChange, type = 'text', placeholder = '', rows = 1) => {
    const isTextarea = rows > 1
    const InputComponent = isTextarea ? 'textarea' : 'input'

    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
        <InputComponent
          type={isTextarea ? undefined : type}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          placeholder={placeholder}
          rows={isTextarea ? rows : undefined}
        />
      </div>
    )
  }

  const handleContentChange = (section, field, value) => {
    setBrandingData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
    setHasUnsavedChanges(true)
  }

  const handleNestedContentChange = (section, field, value) => {
    setBrandingData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
    setHasUnsavedChanges(true)
  }

  const sections = [
    { id: 'global', name: 'Global/Site-wide', icon: FiSettings },
    { id: 'theme', name: 'Theme & Colors', icon: FiImage },
    { id: 'home', name: 'Home Page', icon: FiHome },
    { id: 'services', name: 'Services Page', icon: FiPackage },
    { id: 'consultation', name: 'Consultation Page', icon: FiCalendar },
    { id: 'shop', name: 'Shop Page', icon: FiShoppingBag },
    { id: 'auth', name: 'Authentication Pages', icon: FiUsers },
    { id: 'footer', name: 'Footer', icon: FiSettings }
  ]

  // Create brandingContent from brandingData with fallbacks
  const brandingContent = brandingData || {
    global: {},
    social: {},
    theme: {},
    home: {},
    services: {},
    consultation: {},
    shop: {},
    auth: {},
    cart: {},
    productDetail: {},
    footer: {}
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Branding & Content Management</h2>
          <p className="text-gray-600 mt-1">
            Customize all text content, images, and branding elements across your website.
            Changes are automatically saved and will persist after page refresh.
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadBrandingData}
            disabled={loading}
            className="flex items-center px-4 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-all duration-200 cursor-pointer disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          {hasUnsavedChanges && (
            <button
              onClick={handleSaveChanges}
              disabled={saving}
              className="flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:opacity-50"
            >
              <FiSave className={`w-4 h-4 mr-2 ${saving ? 'animate-pulse' : ''}`} />
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          )}
          <button
            onClick={() => setShowModal(true)}
            className="flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-xl hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            <FiEdit className="w-4 h-4 mr-2" />
            Edit Branding
          </button>
        </div>
      </div>

      {/* Tips Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <FiEdit className="w-5 h-5 text-blue-600 mt-0.5" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Branding Management Tips</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p className="mb-2">Use this section to customize all text content across your website:</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li><strong>Global:</strong> Site name, contact info, and social media links</li>
                <li><strong>Home:</strong> Hero section, features, and call-to-action content</li>
                <li><strong>Services:</strong> Service descriptions and benefits</li>
                <li><strong>Shop:</strong> Product page headers and newsletter content</li>
                <li><strong>Consultation:</strong> Form labels and information text</li>
                <li><strong>Login/Signup:</strong> Authentication page content</li>
                <li><strong>Cart:</strong> Shopping cart and checkout text</li>
                <li><strong>Product Detail:</strong> Product page labels and sections</li>
                <li><strong>User Dashboard:</strong> Dashboard labels and messages</li>
                <li><strong>Footer:</strong> Footer description and section titles</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="flex flex-wrap gap-2">
          {sections.map((section) => {
            const Icon = section.icon
            return (
              <button
                key={section.id}
                onClick={() => {
                  setActiveBrandingSection(section.id)
                  // Scroll to top when switching sections for better UX
                  window.scrollTo({ top: 0, behavior: 'smooth' })
                }}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors duration-200 ${
                  activeBrandingSection === section.id
                    ? 'bg-yellow-50 text-yellow-700 border border-yellow-200'
                    : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-transparent'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {section.name}
              </button>
            )
          })}
        </div>
      </div>

      {/* Content Sections */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        {activeBrandingSection === 'global' && (
          <div className="space-y-6">
            {/* Site Information */}
            <div>
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Site Information</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Site Name', brandingContent.global.siteName, (value) => handleContentChange('global', 'siteName', value))}
                {renderInputField('Phone Number', brandingContent.global.phone, (value) => handleContentChange('global', 'phone', value), 'tel')}
                {renderInputField('Email Address', brandingContent.global.email, (value) => handleContentChange('global', 'email', value), 'email')}
                {renderInputField('Address', brandingContent.global.address, (value) => handleContentChange('global', 'address', value), 'text', '', 3)}
              </div>
            </div>

            {/* Brand Assets */}
            <div>
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Brand Assets</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Logo URL', brandingContent.global.logo, (value) => handleContentChange('global', 'logo', value), 'url')}
                {renderInputField('Favicon URL', brandingContent.global.favicon, (value) => handleContentChange('global', 'favicon', value), 'url')}
              </div>
            </div>

            {/* Social Media */}
            <div>
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Social Media</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {renderInputField('Instagram URL', brandingContent.social.instagram, (value) => handleContentChange('social', 'instagram', value), 'url', 'https://instagram.com/username')}
                {renderInputField('Facebook URL', brandingContent.social.facebook, (value) => handleContentChange('social', 'facebook', value), 'url', 'https://facebook.com/page')}
                {renderInputField('Twitter URL', brandingContent.social.twitter, (value) => handleContentChange('social', 'twitter', value), 'url', 'https://twitter.com/username')}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'theme' && (
          <div className="space-y-6">
            {/* Theme Colors */}
            <div>
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Theme Colors</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInputField('Primary Color', brandingContent.theme.primaryColor, (value) => handleContentChange('theme', 'primaryColor', value), 'color')}
                {renderInputField('Secondary Color', brandingContent.theme.secondaryColor, (value) => handleContentChange('theme', 'secondaryColor', value), 'color')}
                {renderInputField('Accent Color', brandingContent.theme.accentColor, (value) => handleContentChange('theme', 'accentColor', value), 'color')}
                {renderInputField('Background Color', brandingContent.theme.backgroundColor, (value) => handleContentChange('theme', 'backgroundColor', value), 'color')}
                {renderInputField('Text Color', brandingContent.theme.textColor, (value) => handleContentChange('theme', 'textColor', value), 'color')}
                {renderInputField('Secondary Text Color', brandingContent.theme.textSecondaryColor, (value) => handleContentChange('theme', 'textSecondaryColor', value), 'color')}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'home' && (
          <div className="space-y-6">
            {/* Hero Section */}
            <div>
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Hero Section</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Hero Image URL', brandingContent.home.heroImage, (value) => handleContentChange('home', 'heroImage', value), 'url')}
                {renderInputField('Hero Title', brandingContent.home.heroTitle, (value) => handleContentChange('home', 'heroTitle', value))}
                {renderInputField('Hero Subtitle', brandingContent.home.heroSubtitle, (value) => handleContentChange('home', 'heroSubtitle', value), 'text', '', 3)}

                {brandingContent.home.heroImage && (
                  <div className="mt-2">
                    <img
                      src={brandingContent.home.heroImage}
                      alt="Hero preview"
                      className="w-full h-32 object-cover rounded-lg"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Testimonials Section */}
            <div>
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Testimonials Section</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Testimonial Heading', brandingContent.home.testimonialHeading, (value) => handleContentChange('home', 'testimonialHeading', value))}
              </div>
            </div>

          </div>
        )}

        {activeBrandingSection === 'services' && (
          <div className="space-y-6">
            {/* Services Page Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiCalendar className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Services Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Services Page Title', brandingContent.services.servicesTitle, (value) => handleContentChange('services', 'servicesTitle', value))}
                {renderInputField('Services Page Subtitle', brandingContent.services.servicesSubtitle, (value) => handleContentChange('services', 'servicesSubtitle', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'consultation' && (
          <div className="space-y-6">
            {/* Consultation Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiCalendar className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Consultation Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Consultation Title', brandingContent.consultation.consultationTitle, (value) => handleContentChange('consultation', 'consultationTitle', value))}
                {renderInputField('Consultation Subtitle', brandingContent.consultation.consultationSubtitle, (value) => handleContentChange('consultation', 'consultationSubtitle', value))}
                {renderInputField('Consultation Form Title', brandingContent.consultation.consultationFormTitle, (value) => handleContentChange('consultation', 'consultationFormTitle', value))}
                {renderInputField('Consultation Form Subtitle', brandingContent.consultation.consultationFormSubtitle, (value) => handleContentChange('consultation', 'consultationFormSubtitle', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'shop' && (
          <div className="space-y-6">
            {/* Shop Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiShoppingBag className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Shop Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Shop Page Title', brandingContent.shop.shopTitle, (value) => handleContentChange('shop', 'shopTitle', value))}
                {renderInputField('Shop Page Subtitle', brandingContent.shop.shopSubtitle, (value) => handleContentChange('shop', 'shopSubtitle', value))}
                {renderInputField('Featured Products Title', brandingContent.shop.shopFeaturedTitle, (value) => handleContentChange('shop', 'shopFeaturedTitle', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'auth' && (
          <div className="space-y-6">
            {/* Login Page */}
            <div>
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Login Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Login Page Title', brandingContent.auth.loginTitle, (value) => handleContentChange('auth', 'loginTitle', value))}
                {renderInputField('Login Page Subtitle', brandingContent.auth.loginSubtitle, (value) => handleContentChange('auth', 'loginSubtitle', value))}
              </div>
            </div>

            {/* Signup Page */}
            <div>
              <div className="flex items-center mb-4">
                <FiUserCheck className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Signup Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Signup Page Title', brandingContent.auth.signupTitle, (value) => handleContentChange('auth', 'signupTitle', value))}
                {renderInputField('Signup Page Subtitle', brandingContent.auth.signupSubtitle, (value) => handleContentChange('auth', 'signupSubtitle', value))}
                {renderInputField('Agree Terms Text', brandingContent.auth.agreeTermsText, (value) => handleContentChange('auth', 'agreeTermsText', value))}
                {renderInputField('Terms Link Text', brandingContent.auth.termsLinkText, (value) => handleContentChange('auth', 'termsLinkText', value))}
                {renderInputField('And Text', brandingContent.auth.andText, (value) => handleContentChange('auth', 'andText', value))}
                {renderInputField('Privacy Link Text', brandingContent.auth.privacyLinkText, (value) => handleContentChange('auth', 'privacyLinkText', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'footer' && (
          <div className="space-y-6">
            {/* Footer Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Footer Content</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Footer Description', brandingContent.footer.footerDescription, (value) => handleContentChange('footer', 'footerDescription', value), 'text', '', 3)}
                {renderInputField('Copyright Text', brandingContent.footer.footerCopyright, (value) => handleContentChange('footer', 'footerCopyright', value))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <FiRefreshCw className="w-6 h-6 animate-spin text-indigo-500" />
            <span className="text-gray-600">Loading branding data...</span>
          </div>
        </div>
      )}

      {/* Branding Modal */}
      <BrandingModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSave={handleSaveBranding}
        brandingData={brandingData}
        isEdit={!!brandingData}
      />
    </div>
  )
}

export default AdminBranding
