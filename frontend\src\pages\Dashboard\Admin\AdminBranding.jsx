import { useState, useEffect } from 'react'
import {
  FiEdit, FiImage, FiPackage, FiCalendar, FiShoppingBag, FiUsers,
  FiUserCheck, FiBarChart, FiSettings, FiHome, FiPlus, FiRefreshCw, FiSave
} from 'react-icons/fi'
import BrandingModal from '../../../components/Modals/BrandingModal'
import { adminService } from '../../../services'
import brandingService from '../../../services/brandingService'
import { useBranding } from '../../../contexts/BrandingContext'

const AdminBranding = ({
  showToast,
  branding
}) => {
  const { refreshBranding } = useBranding()
  const [brandingData, setBrandingData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [activeBrandingSection, setActiveBrandingSection] = useState('global')
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  useEffect(() => {
    loadBrandingData()
  }, [])

  const loadBrandingData = async () => {
    try {
      setLoading(true)
      console.log('Loading complete branding data...')
      const response = await brandingService.getCompleteBranding()
      console.log('Branding response:', response)

      if (response.success && response.data) {
        // Structure the data for editing
        const { branding: brandingContent, business, theme, site } = response.data
        console.log('Branding content:', brandingContent)
        console.log('Business data:', business)
        console.log('Theme data:', theme)

        setBrandingData({
          // Business Information
          global: {
            siteName: business?.name || '',
            tagline: business?.tagline || '',
            phone: business?.phone || '',
            email: business?.email || '',
            address: business?.address?.full || '',
            logo: brandingContent?.logo || '',
            favicon: brandingContent?.favicon || ''
          },

          // Social Media
          social: {
            instagram: business?.social?.instagram || '',
            facebook: business?.social?.facebook || '',
            twitter: business?.social?.twitter || ''
          },

          // Theme Settings
          theme: {
            primaryColor: theme?.colors?.primary || '#008000',
            secondaryColor: theme?.colors?.secondary || '#f3d016',
            accentColor: theme?.colors?.accent || '#006600',
            backgroundColor: theme?.colors?.background || '#ffffff',
            textColor: theme?.colors?.text || '#000000',
            textSecondaryColor: theme?.colors?.textSecondary || '#666666'
          },

          // Home Page Content
          home: {
            heroImage: brandingContent?.heroImage || '',
            heroTitle: brandingContent?.heroTitle || '',
            heroSubtitle: brandingContent?.heroSubtitle || '',
            aboutTitle: brandingContent?.aboutTitle || '',
            aboutText: brandingContent?.aboutText || '',
            servicesTitle: brandingContent?.servicesTitle || '',
            servicesSubtitle: brandingContent?.servicesSubtitle || '',
            servicesDescription: brandingContent?.servicesDescription || ''
          },

          // Services Content
          services: {
            consultationTitle: brandingContent?.consultationTitle || '',
            consultationSubtitle: brandingContent?.consultationSubtitle || '',
            consultationDescription: brandingContent?.consultationDescription || '',
            consultationFormTitle: brandingContent?.consultationFormTitle || '',
            consultationFormSubtitle: brandingContent?.consultationFormSubtitle || ''
          },

          // Shop Content
          shop: {
            shopTitle: brandingContent?.shopTitle || '',
            shopSubtitle: brandingContent?.shopSubtitle || '',
            shopDescription: brandingContent?.shopDescription || '',
            shopFeaturedTitle: brandingContent?.shopFeaturedTitle || ''
          },

          // Dashboard Content
          dashboard: {
            dashboardWelcome: brandingContent?.dashboardWelcome || '',
            dashboardOverviewTitle: brandingContent?.dashboardOverviewTitle || '',
            dashboardAppointmentsTitle: brandingContent?.dashboardAppointmentsTitle || '',
            dashboardOrdersTitle: brandingContent?.dashboardOrdersTitle || '',
            dashboardFavoritesTitle: brandingContent?.dashboardFavoritesTitle || '',
            dashboardProfileTitle: brandingContent?.dashboardProfileTitle || ''
          },

          // Authentication Content
          auth: {
            loginTitle: brandingContent?.loginTitle || '',
            loginSubtitle: brandingContent?.loginSubtitle || '',
            signupTitle: brandingContent?.signupTitle || '',
            signupSubtitle: brandingContent?.signupSubtitle || ''
          },

          // Navigation & Buttons
          navigation: {
            navHome: brandingContent?.navHome || '',
            navServices: brandingContent?.navServices || '',
            navShop: brandingContent?.navShop || '',
            navConsultation: brandingContent?.navConsultation || '',
            bookNowButton: brandingContent?.bookNowButton || '',
            shopNowButton: brandingContent?.shopNowButton || '',
            learnMoreButton: brandingContent?.learnMoreButton || ''
          },

          // Footer Content
          footer: {
            footerQuickLinks: brandingContent?.footerQuickLinks || '',
            footerContact: brandingContent?.footerContact || '',
            footerFollowUs: brandingContent?.footerFollowUs || '',
            footerCopyright: brandingContent?.footerCopyright || ''
          }
        })

        console.log('Structured branding data set successfully')
      } else {
        throw new Error('Failed to load complete branding data')
      }
    } catch (error) {
      console.error('Error loading branding data:', error)
      showToast('Error loading branding data', 'error')
      setBrandingData({})
    } finally {
      setLoading(false)
    }
  }

  const handleSaveBranding = async (formData) => {
    try {
      setSaving(true)

      // Transform the form data to match the backend structure
      const brandingPayload = {
        global: {
          siteName: formData.global?.siteName,
          tagline: formData.global?.tagline,
          phone: formData.global?.phone,
          email: formData.global?.email,
          address: formData.global?.address,
          logo: formData.global?.logo,
          favicon: formData.global?.favicon,
          instagram: formData.social?.instagram,
          facebook: formData.social?.facebook,
          twitter: formData.social?.twitter
        },
        theme: {
          colors: {
            primary: formData.theme?.primaryColor,
            secondary: formData.theme?.secondaryColor,
            accent: formData.theme?.accentColor,
            background: formData.theme?.backgroundColor,
            text: formData.theme?.textColor,
            textSecondary: formData.theme?.textSecondaryColor
          }
        },
        home: formData.home,
        services: formData.services,
        shop: formData.shop,
        dashboard: formData.dashboard,
        auth: formData.auth,
        navigation: formData.navigation,
        footer: formData.footer
      }

      await adminService.updateBranding(brandingPayload)
      setBrandingData(formData)
      setHasUnsavedChanges(false)
      showToast('Branding updated successfully!', 'success')
      // Refresh branding context to apply changes immediately
      await refreshBranding()
    } catch (error) {
      console.error('Error saving branding:', error)
      showToast('Error saving branding. Please try again.', 'error')
    } finally {
      setSaving(false)
    }
  }

  const handleSaveChanges = async () => {
    if (!hasUnsavedChanges || !brandingData) return

    try {
      setSaving(true)

      // Transform the form data to match the backend structure
      const brandingPayload = {
        global: {
          siteName: brandingData.global?.siteName,
          tagline: brandingData.global?.tagline,
          phone: brandingData.global?.phone,
          email: brandingData.global?.email,
          address: brandingData.global?.address,
          logo: brandingData.global?.logo,
          favicon: brandingData.global?.favicon,
          instagram: brandingData.social?.instagram,
          facebook: brandingData.social?.facebook,
          twitter: brandingData.social?.twitter
        },
        theme: {
          colors: {
            primary: brandingData.theme?.primaryColor,
            secondary: brandingData.theme?.secondaryColor,
            accent: brandingData.theme?.accentColor,
            background: brandingData.theme?.backgroundColor,
            text: brandingData.theme?.textColor,
            textSecondary: brandingData.theme?.textSecondaryColor
          }
        },
        home: brandingData.home,
        services: brandingData.services,
        shop: brandingData.shop,
        dashboard: brandingData.dashboard,
        auth: brandingData.auth,
        navigation: brandingData.navigation,
        footer: brandingData.footer
      }

      await adminService.updateBranding(brandingPayload)
      setHasUnsavedChanges(false)
      showToast('Branding changes saved successfully!', 'success')
      // Refresh branding context to apply changes immediately
      await refreshBranding()
    } catch (error) {
      console.error('Error saving branding changes:', error)
      showToast('Error saving changes. Please try again.', 'error')
    } finally {
      setSaving(false)
    }
  }

  const renderInputField = (label, value, onChange, type = 'text', placeholder = '', rows = 1) => {
    const isTextarea = rows > 1
    const InputComponent = isTextarea ? 'textarea' : 'input'

    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
        <InputComponent
          type={isTextarea ? undefined : type}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          placeholder={placeholder}
          rows={isTextarea ? rows : undefined}
        />
      </div>
    )
  }

  const handleContentChange = (section, field, value) => {
    setBrandingData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
    setHasUnsavedChanges(true)
  }

  const handleNestedContentChange = (section, field, value) => {
    setBrandingData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
    setHasUnsavedChanges(true)
  }

  const sections = [
    { id: 'global', name: 'Global/Site-wide', icon: FiSettings },
    { id: 'theme', name: 'Theme & Colors', icon: FiImage },
    { id: 'home', name: 'Home Page', icon: FiHome },
    { id: 'services', name: 'Services Page', icon: FiPackage },
    { id: 'shop', name: 'Shop Page', icon: FiShoppingBag },
    { id: 'dashboard', name: 'Dashboard Content', icon: FiBarChart },
    { id: 'auth', name: 'Authentication Pages', icon: FiUsers },
    { id: 'navigation', name: 'Navigation & Buttons', icon: FiEdit },
    { id: 'footer', name: 'Footer', icon: FiSettings }
  ]

  // Create brandingContent from brandingData with fallbacks
  const brandingContent = brandingData || {
    global: {},
    social: {},
    theme: {},
    home: {},
    services: {},
    shop: {},
    dashboard: {},
    auth: {},
    navigation: {},
    footer: {}
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Branding & Content Management</h2>
          <p className="text-gray-600 mt-1">
            Customize all text content, images, and branding elements across your website.
            Changes are automatically saved and will persist after page refresh.
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadBrandingData}
            disabled={loading}
            className="flex items-center px-4 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-all duration-200 cursor-pointer disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          {hasUnsavedChanges && (
            <button
              onClick={handleSaveChanges}
              disabled={saving}
              className="flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:opacity-50"
            >
              <FiSave className={`w-4 h-4 mr-2 ${saving ? 'animate-pulse' : ''}`} />
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          )}
          <button
            onClick={() => setShowModal(true)}
            className="flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-xl hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            <FiEdit className="w-4 h-4 mr-2" />
            Edit Branding
          </button>
        </div>
      </div>

      {/* Tips Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <FiEdit className="w-5 h-5 text-blue-600 mt-0.5" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Branding Management Tips</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p className="mb-2">Use this section to customize all text content across your website:</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li><strong>Global:</strong> Site name, contact info, and social media links</li>
                <li><strong>Home:</strong> Hero section, features, and call-to-action content</li>
                <li><strong>Services:</strong> Service descriptions and benefits</li>
                <li><strong>Shop:</strong> Product page headers and newsletter content</li>
                <li><strong>Consultation:</strong> Form labels and information text</li>
                <li><strong>Login/Signup:</strong> Authentication page content</li>
                <li><strong>Cart:</strong> Shopping cart and checkout text</li>
                <li><strong>Product Detail:</strong> Product page labels and sections</li>
                <li><strong>User Dashboard:</strong> Dashboard labels and messages</li>
                <li><strong>Footer:</strong> Footer description and section titles</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="flex flex-wrap gap-2">
          {sections.map((section) => {
            const Icon = section.icon
            return (
              <button
                key={section.id}
                onClick={() => {
                  setActiveBrandingSection(section.id)
                  // Scroll to top when switching sections for better UX
                  window.scrollTo({ top: 0, behavior: 'smooth' })
                }}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors duration-200 ${
                  activeBrandingSection === section.id
                    ? 'bg-yellow-50 text-yellow-700 border border-yellow-200'
                    : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-transparent'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {section.name}
              </button>
            )
          })}
        </div>
      </div>

      {/* Content Sections */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        {activeBrandingSection === 'global' && (
          <div className="space-y-6">
            {/* Site Information */}
            <div>
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Site Information</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Site Name', brandingContent.global.siteName, (value) => handleContentChange('global', 'siteName', value))}
                {renderInputField('Phone Number', brandingContent.global.phone, (value) => handleContentChange('global', 'phone', value), 'tel')}
                {renderInputField('Email Address', brandingContent.global.email, (value) => handleContentChange('global', 'email', value), 'email')}
                {renderInputField('Address', brandingContent.global.address, (value) => handleContentChange('global', 'address', value), 'text', '', 3)}
              </div>
            </div>

            {/* Brand Assets */}
            <div>
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Brand Assets</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Logo URL', brandingContent.global.logo, (value) => handleContentChange('global', 'logo', value), 'url')}
                {renderInputField('Favicon URL', brandingContent.global.favicon, (value) => handleContentChange('global', 'favicon', value), 'url')}
              </div>
            </div>

            {/* Social Media */}
            <div>
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Social Media</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {renderInputField('Instagram URL', brandingContent.social.instagram, (value) => handleContentChange('social', 'instagram', value), 'url', 'https://instagram.com/username')}
                {renderInputField('Facebook URL', brandingContent.social.facebook, (value) => handleContentChange('social', 'facebook', value), 'url', 'https://facebook.com/page')}
                {renderInputField('Twitter URL', brandingContent.social.twitter, (value) => handleContentChange('social', 'twitter', value), 'url', 'https://twitter.com/username')}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'theme' && (
          <div className="space-y-6">
            {/* Theme Colors */}
            <div>
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Theme Colors</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInputField('Primary Color', brandingContent.theme.primaryColor, (value) => handleContentChange('theme', 'primaryColor', value), 'color')}
                {renderInputField('Secondary Color', brandingContent.theme.secondaryColor, (value) => handleContentChange('theme', 'secondaryColor', value), 'color')}
                {renderInputField('Accent Color', brandingContent.theme.accentColor, (value) => handleContentChange('theme', 'accentColor', value), 'color')}
                {renderInputField('Background Color', brandingContent.theme.backgroundColor, (value) => handleContentChange('theme', 'backgroundColor', value), 'color')}
                {renderInputField('Text Color', brandingContent.theme.textColor, (value) => handleContentChange('theme', 'textColor', value), 'color')}
                {renderInputField('Secondary Text Color', brandingContent.theme.textSecondaryColor, (value) => handleContentChange('theme', 'textSecondaryColor', value), 'color')}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'home' && (
          <div className="space-y-6">
            {/* Hero Section */}
            <div>
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Hero Section</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Hero Image URL', brandingContent.home.heroImage, (value) => handleContentChange('home', 'heroImage', value), 'url')}
                {renderInputField('Hero Title', brandingContent.home.heroTitle, (value) => handleContentChange('home', 'heroTitle', value))}
                {renderInputField('Hero Subtitle', brandingContent.home.heroSubtitle, (value) => handleContentChange('home', 'heroSubtitle', value), 'text', '', 3)}

                {brandingContent.home.heroImage && (
                  <div className="mt-2">
                    <img
                      src={brandingContent.home.heroImage}
                      alt="Hero preview"
                      className="w-full h-32 object-cover rounded-lg"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* About Section */}
            <div>
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">About Section</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('About Title', brandingContent.home.aboutTitle, (value) => handleContentChange('home', 'aboutTitle', value))}
                {renderInputField('About Text', brandingContent.home.aboutText, (value) => handleContentChange('home', 'aboutText', value), 'text', '', 4)}
              </div>
            </div>

            {/* Services Section */}
            <div>
              <div className="flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Services Section</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Services Title', brandingContent.home.servicesTitle, (value) => handleContentChange('home', 'servicesTitle', value))}
                {renderInputField('Services Subtitle', brandingContent.home.servicesSubtitle, (value) => handleContentChange('home', 'servicesSubtitle', value))}
                {renderInputField('Services Description', brandingContent.home.servicesDescription, (value) => handleContentChange('home', 'servicesDescription', value), 'text', '', 3)}
              </div>
            </div>

          </div>
        )}

        {activeBrandingSection === 'services' && (
          <div className="space-y-6">
            {/* Consultation Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiCalendar className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Consultation Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Consultation Title', brandingContent.services.consultationTitle, (value) => handleContentChange('services', 'consultationTitle', value))}
                {renderInputField('Consultation Subtitle', brandingContent.services.consultationSubtitle, (value) => handleContentChange('services', 'consultationSubtitle', value))}
                {renderInputField('Consultation Description', brandingContent.services.consultationDescription, (value) => handleContentChange('services', 'consultationDescription', value), 'text', '', 3)}
                {renderInputField('Consultation Form Title', brandingContent.services.consultationFormTitle, (value) => handleContentChange('services', 'consultationFormTitle', value))}
                {renderInputField('Consultation Form Subtitle', brandingContent.services.consultationFormSubtitle, (value) => handleContentChange('services', 'consultationFormSubtitle', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'shop' && (
          <div className="space-y-6">
            {/* Shop Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiShoppingBag className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Shop Page</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Shop Title', brandingContent.shop.shopTitle, (value) => handleContentChange('shop', 'shopTitle', value))}
                {renderInputField('Shop Subtitle', brandingContent.shop.shopSubtitle, (value) => handleContentChange('shop', 'shopSubtitle', value))}
                {renderInputField('Shop Description', brandingContent.shop.shopDescription, (value) => handleContentChange('shop', 'shopDescription', value), 'text', '', 3)}
                {renderInputField('Featured Products Title', brandingContent.shop.shopFeaturedTitle, (value) => handleContentChange('shop', 'shopFeaturedTitle', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'dashboard' && (
          <div className="space-y-6">
            {/* Dashboard Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiBarChart className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Dashboard Content</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Dashboard Welcome Message', brandingContent.dashboard.dashboardWelcome, (value) => handleContentChange('dashboard', 'dashboardWelcome', value))}
                {renderInputField('Overview Section Title', brandingContent.dashboard.dashboardOverviewTitle, (value) => handleContentChange('dashboard', 'dashboardOverviewTitle', value))}
                {renderInputField('Appointments Section Title', brandingContent.dashboard.dashboardAppointmentsTitle, (value) => handleContentChange('dashboard', 'dashboardAppointmentsTitle', value))}
                {renderInputField('Orders Section Title', brandingContent.dashboard.dashboardOrdersTitle, (value) => handleContentChange('dashboard', 'dashboardOrdersTitle', value))}
                {renderInputField('Favorites Section Title', brandingContent.dashboard.dashboardFavoritesTitle, (value) => handleContentChange('dashboard', 'dashboardFavoritesTitle', value))}
                {renderInputField('Profile Section Title', brandingContent.dashboard.dashboardProfileTitle, (value) => handleContentChange('dashboard', 'dashboardProfileTitle', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'auth' && (
          <div className="space-y-6">
            {/* Authentication Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Authentication Pages</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Login Title', brandingContent.auth.loginTitle, (value) => handleContentChange('auth', 'loginTitle', value))}
                {renderInputField('Login Subtitle', brandingContent.auth.loginSubtitle, (value) => handleContentChange('auth', 'loginSubtitle', value))}
                {renderInputField('Signup Title', brandingContent.auth.signupTitle, (value) => handleContentChange('auth', 'signupTitle', value))}
                {renderInputField('Signup Subtitle', brandingContent.auth.signupSubtitle, (value) => handleContentChange('auth', 'signupSubtitle', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'navigation' && (
          <div className="space-y-6">
            {/* Navigation Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Navigation & Buttons</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Home Navigation', brandingContent.navigation.navHome, (value) => handleContentChange('navigation', 'navHome', value))}
                {renderInputField('Services Navigation', brandingContent.navigation.navServices, (value) => handleContentChange('navigation', 'navServices', value))}
                {renderInputField('Shop Navigation', brandingContent.navigation.navShop, (value) => handleContentChange('navigation', 'navShop', value))}
                {renderInputField('Consultation Navigation', brandingContent.navigation.navConsultation, (value) => handleContentChange('navigation', 'navConsultation', value))}
                {renderInputField('Book Now Button', brandingContent.navigation.bookNowButton, (value) => handleContentChange('navigation', 'bookNowButton', value))}
                {renderInputField('Shop Now Button', brandingContent.navigation.shopNowButton, (value) => handleContentChange('navigation', 'shopNowButton', value))}
                {renderInputField('Learn More Button', brandingContent.navigation.learnMoreButton, (value) => handleContentChange('navigation', 'learnMoreButton', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'footer' && (
          <div className="space-y-6">
            {/* Footer Content */}
            <div>
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Footer Content</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Quick Links Title', brandingContent.footer.footerQuickLinks, (value) => handleContentChange('footer', 'footerQuickLinks', value))}
                {renderInputField('Contact Title', brandingContent.footer.footerContact, (value) => handleContentChange('footer', 'footerContact', value))}
                {renderInputField('Follow Us Title', brandingContent.footer.footerFollowUs, (value) => handleContentChange('footer', 'footerFollowUs', value))}
                {renderInputField('Copyright Text', brandingContent.footer.footerCopyright, (value) => handleContentChange('footer', 'footerCopyright', value))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <FiRefreshCw className="w-6 h-6 animate-spin text-indigo-500" />
            <span className="text-gray-600">Loading branding data...</span>
          </div>
        </div>
      )}

      {/* Branding Modal */}
      <BrandingModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSave={handleSaveBranding}
        brandingData={brandingData}
        isEdit={!!brandingData}
      />
    </div>
  )
}

export default AdminBranding
