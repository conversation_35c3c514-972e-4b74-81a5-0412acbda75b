import mongoose, { Schema, Document } from 'mongoose';

export interface IBranding extends Document {
  _id: string;
  global: {
    siteName: string;
    tagline: string;
    logo: string;
    phone: string;
    email: string;
    address: string;
    instagram: string;
    facebook: string;
    twitter: string;
    youtube: string;
  };
  home: {
    heroTitle: string;
    heroSubtitle: string;
    heroImage: string;
    aboutTitle: string;
    aboutText: string;
    featuredServices: Array<{
      title: string;
      description: string;
      image: string;
    }>;
    testimonialHeading: string;
  };
  services: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    serviceCategories: Array<{
      name: string;
      description: string;
    }>;
    // Individual service content
    serviceLocMaintenance: string;
    serviceLocMaintenanceDesc: string;
    serviceStarterLocs: string;
    serviceStarterLocsDesc: string;
    serviceLocStyling: string;
    serviceLocStylingDesc: string;
    serviceNaturalHairCare: string;
    serviceNaturalHairCareDesc: string;
  };
  shop: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    featuredCollectionTitle: string;
  };
  consultation: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    formTitle: string;
    formSubtitle: string;
  };
  login: {
    pageTitle: string;
    pageSubtitle: string;
  };
  signup: {
    pageTitle: string;
    pageSubtitle: string;
    agreeTermsText: string;
    termsLinkText: string;
    andText: string;
    privacyLinkText: string;
  };
  cart: {
    pageTitle: string;
    emptyCartMessage: string;
    freeShippingThreshold: string;
    shippingCalculated: string;
  };
  productDetail: {
    addToCartButton: string;
    quantityLabel: string;
    overviewTab: string;
    ingredientsTab: string;
    reviewsTab: string;
  };
  footer: {
    description: string;
    copyrightText: string;
    quickLinks: Array<{
      text: string;
      url: string;
    }>;
  };
  // Dashboard content
  dashboard: {
    welcomeMessage: string;
    overviewTitle: string;
    appointmentsTitle: string;
    ordersTitle: string;
    favoritesTitle: string;
    profileTitle: string;
    nextAppointment: string;
    recentOrders: string;
    loyaltyTitle: string;
  };
  // Buttons and UI elements
  buttons: {
    bookNow: string;
    shopNow: string;
    learnMore: string;
    viewAll: string;
    continueShopping: string;
    proceedToCheckout: string;
    addToCart: string;
    scheduleConsultation: string;
    writeReview: string;
  };
  // Navigation
  navigation: {
    home: string;
    services: string;
    shop: string;
    consultation: string;
    login: string;
    signup: string;
    dashboard: string;
  };
  // Testimonials and reviews
  testimonials: {
    title: string;
    subtitle: string;
  };
  reviews: {
    title: string;
  };
  // Messages
  messages: {
    loading: string;
    error: string;
    notFound: string;
    comingSoon: string;
    cartShipping: string;
  };
  // Business information (stored in branding for admin editing)
  business: {
    name: string;
    tagline: string;
    description: string;
    phone: string;
    email: string;
    address: {
      street: string;
      city: string;
      state: string;
      zip: string;
      full: string;
    };
    social: {
      instagram: string;
      facebook: string;
      twitter: string;
    };
    hours: {
      monday: string;
      tuesday: string;
      wednesday: string;
      thursday: string;
      friday: string;
      saturday: string;
      sunday: string;
    };
  };
  // Theme settings (stored in branding for admin editing)
  theme: {
    colors: {
      primary: string;
      secondary: string;
      accent: string;
      background: string;
      text: string;
      textSecondary: string;
    };
    fonts: {
      primary: string;
      secondary: string;
      heading: string;
    };
  };
  // SEO and site settings (stored in branding for admin editing)
  site: {
    seo: {
      title: string;
      description: string;
      keywords: string;
    };
    features: {
      onlineBooking: boolean;
      ecommerce: boolean;
      loyaltyProgram: boolean;
      giftCards: boolean;
      reviews: boolean;
      blog: boolean;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

const brandingSchema = new Schema<IBranding>({
  global: {
    siteName: { type: String, default: 'MicroLocs' },
    tagline: { type: String, default: 'Professional Hair Care Services' },
    logo: { type: String, default: '' },
    phone: { type: String, default: '' },
    email: { type: String, default: '' },
    address: { type: String, default: '' },
    instagram: { type: String, default: '' },
    facebook: { type: String, default: '' },
    twitter: { type: String, default: '' },
    youtube: { type: String, default: '' }
  },
  home: {
    heroTitle: { type: String, default: 'Beautiful Locs, Beautiful You' },
    heroSubtitle: { type: String, default: 'Professional loc services and natural hair care by Tina' },
    heroImage: { type: String, default: 'https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600' },
    aboutTitle: { type: String, default: 'About Goldie Locs' },
    aboutText: { type: String, default: 'With years of experience in natural hair care and loc maintenance, Tina provides personalized services to help you achieve and maintain beautiful, healthy locs.' },
    featuredServices: [{
      title: { type: String, required: true },
      description: { type: String, required: true },
      image: { type: String, required: true }
    }],
    testimonialHeading: { type: String, default: 'What Our Clients Say' }
  },
  services: {
    pageTitle: { type: String, default: 'Our Professional Services' },
    pageSubtitle: { type: String, default: 'Expert loc care and natural hair services tailored to your unique needs' },
    pageDescription: { type: String, default: 'From starter locs to maintenance and styling, we provide comprehensive care for your natural hair journey.' },
    serviceCategories: [{
      name: { type: String, required: true },
      description: { type: String, required: true }
    }],
    serviceLocMaintenance: { type: String, default: 'Loc Maintenance' },
    serviceLocMaintenanceDesc: { type: String, default: 'Professional maintenance to keep your locs healthy and looking their best' },
    serviceStarterLocs: { type: String, default: 'Starter Locs' },
    serviceStarterLocsDesc: { type: String, default: 'Begin your loc journey with expert guidance and professional techniques' },
    serviceLocStyling: { type: String, default: 'Loc Styling' },
    serviceLocStylingDesc: { type: String, default: 'Creative styling options to showcase your locs for any occasion' },
    serviceNaturalHairCare: { type: String, default: 'Natural Hair Care' },
    serviceNaturalHairCareDesc: { type: String, default: 'Comprehensive care for natural hair health and growth' }
  },
  shop: {
    pageTitle: { type: String, default: 'Premium Hair Care Products' },
    pageSubtitle: { type: String, default: 'Professional-grade products for healthy locs and natural hair' },
    pageDescription: { type: String, default: 'Discover our curated collection of premium hair care products designed specifically for locs and natural hair.' },
    featuredCollectionTitle: { type: String, default: 'Featured Products' }
  },
  consultation: {
    pageTitle: { type: String, default: 'Book Your Consultation' },
    pageSubtitle: { type: String, default: 'Start your loc journey with a personalized consultation' },
    pageDescription: { type: String, default: 'Schedule a one-on-one consultation to discuss your hair goals, assess your hair type, and create a customized care plan.' },
    formTitle: { type: String, default: 'Tell Us About Your Hair Goals' },
    formSubtitle: { type: String, default: 'Help us understand your needs so we can provide the best service' }
  },
  login: {
    pageTitle: { type: String, default: 'Welcome Back' },
    pageSubtitle: { type: String, default: 'Sign in to access your account and manage your appointments' }
  },
  signup: {
    pageTitle: { type: String, default: 'Join Our Community' },
    pageSubtitle: { type: String, default: 'Create your account to book appointments and shop products' },
    agreeTermsText: { type: String, default: 'I agree to the' },
    termsLinkText: { type: String, default: 'Terms of Service' },
    andText: { type: String, default: 'and' },
    privacyLinkText: { type: String, default: 'Privacy Policy' }
  },
  cart: {
    pageTitle: { type: String, default: 'Your Shopping Cart' },
    emptyCartMessage: { type: String, default: 'Your cart is empty. Discover our amazing products!' },
    freeShippingThreshold: { type: String, default: 'Free shipping on orders over $50' },
    shippingCalculated: { type: String, default: 'Shipping calculated at checkout' }
  },
  productDetail: {
    addToCartButton: { type: String, default: 'Add to Cart' },
    quantityLabel: { type: String, default: 'Quantity' },
    overviewTab: { type: String, default: 'Overview' },
    ingredientsTab: { type: String, default: 'Ingredients' },
    reviewsTab: { type: String, default: 'Reviews' }
  },
  footer: {
    description: { type: String, default: 'Professional hair care services' },
    copyrightText: { type: String, default: '© 2024 Goldie Locs By Tina. All rights reserved.' },
    quickLinks: [{
      text: { type: String, required: true },
      url: { type: String, required: true }
    }]
  },
  // Dashboard content
  dashboard: {
    welcomeMessage: { type: String, default: 'Welcome back to your hair care journey!' },
    overviewTitle: { type: String, default: 'Your Hair Care Overview' },
    appointmentsTitle: { type: String, default: 'Your Appointments' },
    ordersTitle: { type: String, default: 'Your Orders' },
    favoritesTitle: { type: String, default: 'Your Favorites' },
    profileTitle: { type: String, default: 'Profile Settings' },
    nextAppointment: { type: String, default: 'Your Next Appointment' },
    recentOrders: { type: String, default: 'Recent Orders' },
    loyaltyTitle: { type: String, default: 'Loyalty Rewards' }
  },
  // Buttons and UI elements
  buttons: {
    bookNow: { type: String, default: 'Book Now' },
    shopNow: { type: String, default: 'Shop Now' },
    learnMore: { type: String, default: 'Learn More' },
    viewAll: { type: String, default: 'View All' },
    continueShopping: { type: String, default: 'Continue Shopping' },
    proceedToCheckout: { type: String, default: 'Proceed to Checkout' },
    addToCart: { type: String, default: 'Add to Cart' },
    scheduleConsultation: { type: String, default: 'Schedule Consultation' },
    writeReview: { type: String, default: 'Write a Review' }
  },
  // Navigation
  navigation: {
    home: { type: String, default: 'Home' },
    services: { type: String, default: 'Services' },
    shop: { type: String, default: 'Shop' },
    consultation: { type: String, default: 'Book Consultation' },
    login: { type: String, default: 'Login' },
    signup: { type: String, default: 'Sign Up' },
    dashboard: { type: String, default: 'Dashboard' }
  },
  // Testimonials and reviews
  testimonials: {
    title: { type: String, default: 'What Our Clients Say' },
    subtitle: { type: String, default: 'Real experiences from our satisfied customers' }
  },
  reviews: {
    title: { type: String, default: 'Customer Reviews' }
  },
  // Messages
  messages: {
    loading: { type: String, default: 'Loading...' },
    error: { type: String, default: 'Something went wrong. Please try again.' },
    notFound: { type: String, default: 'Page not found' },
    comingSoon: { type: String, default: 'Coming soon!' },
    cartShipping: { type: String, default: 'Free shipping on orders over $50' }
  },
  // Business information (stored in branding for admin editing)
  business: {
    name: { type: String, default: 'MicroLocs' },
    tagline: { type: String, default: 'Professional Hair Care Services' },
    description: { type: String, default: 'Professional locs and natural hair care services. Specializing in micro locs, traditional locs, and natural hair maintenance.' },
    phone: { type: String, default: '(*************' },
    email: { type: String, default: '<EMAIL>' },
    address: {
      street: { type: String, default: '123 Beauty Street' },
      city: { type: String, default: 'Atlanta' },
      state: { type: String, default: 'GA' },
      zip: { type: String, default: '30309' },
      full: { type: String, default: '123 Beauty Street, Atlanta, GA 30309' }
    },
    social: {
      instagram: { type: String, default: 'https://instagram.com/goldielocs' },
      facebook: { type: String, default: 'https://facebook.com/goldielocs' },
      twitter: { type: String, default: 'https://twitter.com/goldielocs' }
    },
    hours: {
      monday: { type: String, default: '9:00 AM - 6:00 PM' },
      tuesday: { type: String, default: '9:00 AM - 6:00 PM' },
      wednesday: { type: String, default: '9:00 AM - 6:00 PM' },
      thursday: { type: String, default: '9:00 AM - 6:00 PM' },
      friday: { type: String, default: '9:00 AM - 6:00 PM' },
      saturday: { type: String, default: '9:00 AM - 4:00 PM' },
      sunday: { type: String, default: 'Closed' }
    }
  },
  // Theme settings (stored in branding for admin editing)
  theme: {
    colors: {
      primary: { type: String, default: '#008000' },
      secondary: { type: String, default: '#f3d016' },
      accent: { type: String, default: '#006600' },
      background: { type: String, default: '#ffffff' },
      text: { type: String, default: '#000000' },
      textSecondary: { type: String, default: '#666666' }
    },
    fonts: {
      primary: { type: String, default: 'Inter, system-ui, sans-serif' },
      secondary: { type: String, default: 'Inter, system-ui, sans-serif' },
      heading: { type: String, default: 'Inter, system-ui, sans-serif' }
    }
  },
  // SEO and site settings (stored in branding for admin editing)
  site: {
    seo: {
      title: { type: String, default: 'Goldie Locs By Tina - Professional Loc Services' },
      description: { type: String, default: 'Professional locs and natural hair care services in Atlanta, GA. Specializing in micro locs, traditional locs, and natural hair maintenance.' },
      keywords: { type: String, default: 'locs, dreadlocks, natural hair, hair care, Atlanta, micro locs, loc maintenance' }
    },
    features: {
      onlineBooking: { type: Boolean, default: true },
      ecommerce: { type: Boolean, default: true },
      loyaltyProgram: { type: Boolean, default: true },
      giftCards: { type: Boolean, default: true },
      reviews: { type: Boolean, default: true },
      blog: { type: Boolean, default: false }
    }
  }
}, {
  timestamps: true
});

export const Branding = mongoose.model<IBranding>('Branding', brandingSchema);
