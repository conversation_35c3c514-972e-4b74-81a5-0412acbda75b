import mongoose, { Schema, Document } from 'mongoose';

export interface IBranding extends Document {
  _id: string;
  global: {
    siteName: string;
    tagline: string;
    logo: string;
    favicon: string;
    phone: string;
    email: string;
    address: string;
    instagram: string;
    facebook: string;
    twitter: string;
    youtube: string;
  };
  home: {
    heroTitle: string;
    heroSubtitle: string;
    heroImage: string;
    aboutTitle: string;
    aboutText: string;
    featuredServices: Array<{
      title: string;
      description: string;
      image: string;
    }>;
    testimonialHeading: string;
  };
  services: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    serviceCategories: Array<{
      name: string;
      description: string;
    }>;
    // Individual service content
    serviceLocMaintenance: string;
    serviceLocMaintenanceDesc: string;
    serviceStarterLocs: string;
    serviceStarterLocsDesc: string;
    serviceLocStyling: string;
    serviceLocStylingDesc: string;
    serviceNaturalHairCare: string;
    serviceNaturalHairCareDesc: string;
  };
  shop: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    featuredCollectionTitle: string;
  };
  consultation: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    formTitle: string;
    formSubtitle: string;
  };
  login: {
    pageTitle: string;
    pageSubtitle: string;
  };
  signup: {
    pageTitle: string;
    pageSubtitle: string;
    agreeTermsText: string;
    termsLinkText: string;
    andText: string;
    privacyLinkText: string;
  };
  cart: {
    pageTitle: string;
    emptyCartMessage: string;
    freeShippingThreshold: string;
    shippingCalculated: string;
  };
  productDetail: {
    addToCartButton: string;
    quantityLabel: string;
    overviewTab: string;
    ingredientsTab: string;
    reviewsTab: string;
  };
  footer: {
    description: string;
    copyrightText: string;
    quickLinks: Array<{name: string; url: string}>;
    contact: string;
    followUs: string;
  };
  // Dashboard content
  dashboard: {
    welcomeMessage: string;
    overviewTitle: string;
    appointmentsTitle: string;
    ordersTitle: string;
    favoritesTitle: string;
    profileTitle: string;
    nextAppointment: string;
    recentOrders: string;
    loyaltyTitle: string;
  };
  // Buttons and UI elements
  buttons: {
    bookNow: string;
    shopNow: string;
    learnMore: string;
    viewAll: string;
    continueShopping: string;
    proceedToCheckout: string;
    addToCart: string;
    scheduleConsultation: string;
    writeReview: string;
  };
  // Navigation
  navigation: {
    home: string;
    services: string;
    shop: string;
    consultation: string;
    login: string;
    signup: string;
    dashboard: string;
  };
  // Testimonials and reviews
  testimonials: {
    title: string;
    subtitle: string;
  };
  reviews: {
    title: string;
  };
  // Messages
  messages: {
    loading: string;
    error: string;
    notFound: string;
    comingSoon: string;
    cartShipping: string;
  };
  // Business information (stored in branding for admin editing)
  business: {
    name: string;
    tagline: string;
    description: string;
    phone: string;
    email: string;
    address: {
      street: string;
      city: string;
      state: string;
      zip: string;
      full: string;
    };
    social: {
      instagram: string;
      facebook: string;
      twitter: string;
    };
    hours: {
      monday: string;
      tuesday: string;
      wednesday: string;
      thursday: string;
      friday: string;
      saturday: string;
      sunday: string;
    };
  };
  // Theme settings (stored in branding for admin editing)
  theme: {
    colors: {
      primary: string;
      secondary: string;
      accent: string;
      background: string;
      text: string;
      textSecondary: string;
    };
    fonts: {
      primary: string;
      secondary: string;
      heading: string;
    };
  };
  // SEO and site settings (stored in branding for admin editing)
  site: {
    seo: {
      title: string;
      description: string;
      keywords: string;
    };
    features: {
      onlineBooking: boolean;
      ecommerce: boolean;
      loyaltyProgram: boolean;
      giftCards: boolean;
      reviews: boolean;
      blog: boolean;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

const brandingSchema = new Schema<IBranding>({
  global: {
    siteName: { type: String, required: true },
    tagline: { type: String, required: true },
    logo: { type: String, required: true },
    favicon: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String, required: true },
    address: { type: String, required: true },
    instagram: { type: String, required: true },
    facebook: { type: String, required: true },
    twitter: { type: String, required: true },
    youtube: { type: String, required: true }
  },
  home: {
    heroTitle: { type: String, required: true },
    heroSubtitle: { type: String, required: true },
    heroImage: { type: String, required: true },
    aboutTitle: { type: String, required: true },
    aboutText: { type: String, required: true },
    featuredServices: [{
      title: { type: String, required: true },
      description: { type: String, required: true },
      image: { type: String, required: true }
    }],
    testimonialHeading: { type: String, required: true }
  },
  services: {
    pageTitle: { type: String, required: true },
    pageSubtitle: { type: String, required: true },
    pageDescription: { type: String, required: true },
    serviceCategories: [{
      name: { type: String, required: true },
      description: { type: String, required: true }
    }],
    serviceLocMaintenance: { type: String, required: true },
    serviceLocMaintenanceDesc: { type: String, required: true },
    serviceStarterLocs: { type: String, required: true },
    serviceStarterLocsDesc: { type: String, required: true },
    serviceLocStyling: { type: String, required: true },
    serviceLocStylingDesc: { type: String, required: true },
    serviceNaturalHairCare: { type: String, required: true },
    serviceNaturalHairCareDesc: { type: String, required: true }
  },
  shop: {
    pageTitle: { type: String, required: true },
    pageSubtitle: { type: String, required: true },
    pageDescription: { type: String, required: true },
    featuredCollectionTitle: { type: String, required: true }
  },
  consultation: {
    pageTitle: { type: String, required: true },
    pageSubtitle: { type: String, required: true },
    pageDescription: { type: String, required: true },
    formTitle: { type: String, required: true },
    formSubtitle: { type: String, required: true }
  },
  login: {
    pageTitle: { type: String, required: true },
    pageSubtitle: { type: String, required: true }
  },
  signup: {
    pageTitle: { type: String, required: true },
    pageSubtitle: { type: String, required: true },
    agreeTermsText: { type: String, required: true },
    termsLinkText: { type: String, required: true },
    andText: { type: String, required: true },
    privacyLinkText: { type: String, required: true }
  },
  cart: {
    pageTitle: { type: String, required: true },
    emptyCartMessage: { type: String, required: true },
    freeShippingThreshold: { type: String, required: true },
    shippingCalculated: { type: String, required: true }
  },
  productDetail: {
    addToCartButton: { type: String, required: true },
    quantityLabel: { type: String, required: true },
    overviewTab: { type: String, required: true },
    ingredientsTab: { type: String, required: true },
    reviewsTab: { type: String, required: true }
  },
  footer: {
    description: { type: String, required: true },
    copyrightText: { type: String, required: true },
    quickLinks: {
      type: [{
        name: { type: String, required: true },
        url: { type: String, required: true }
      }],
      required: true
    },
    contact: { type: String, required: true },
    followUs: { type: String, required: true }
  },
  // Dashboard content
  dashboard: {
    welcomeMessage: { type: String, required: true },
    overviewTitle: { type: String, required: true },
    appointmentsTitle: { type: String, required: true },
    ordersTitle: { type: String, required: true },
    favoritesTitle: { type: String, required: true },
    profileTitle: { type: String, required: true },
    nextAppointment: { type: String, required: true },
    recentOrders: { type: String, required: true },
    loyaltyTitle: { type: String, required: true }
  },
  // Buttons and UI elements
  buttons: {
    bookNow: { type: String, required: true },
    shopNow: { type: String, required: true },
    learnMore: { type: String, required: true },
    viewAll: { type: String, required: true },
    continueShopping: { type: String, required: true },
    proceedToCheckout: { type: String, required: true },
    addToCart: { type: String, required: true },
    scheduleConsultation: { type: String, required: true },
    writeReview: { type: String, required: true }
  },
  // Navigation
  navigation: {
    home: { type: String, required: true },
    services: { type: String, required: true },
    shop: { type: String, required: true },
    consultation: { type: String, required: true },
    login: { type: String, required: true },
    signup: { type: String, required: true },
    dashboard: { type: String, required: true }
  },
  // Testimonials and reviews
  testimonials: {
    title: { type: String, required: true },
    subtitle: { type: String, required: true }
  },
  reviews: {
    title: { type: String, required: true }
  },
  // Messages
  messages: {
    loading: { type: String, required: true },
    error: { type: String, required: true },
    notFound: { type: String, required: true },
    comingSoon: { type: String, required: true },
    cartShipping: { type: String, required: true }
  },
  // Business information (stored in branding for admin editing)
  business: {
    name: { type: String, required: true },
    tagline: { type: String, required: true },
    description: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String, required: true },
    address: {
      street: { type: String, required: true },
      city: { type: String, required: true },
      state: { type: String, required: true },
      zip: { type: String, required: true },
      full: { type: String, required: true }
    },
    social: {
      instagram: { type: String, required: true },
      facebook: { type: String, required: true },
      twitter: { type: String, required: true }
    },
    hours: {
      monday: { type: String, required: true },
      tuesday: { type: String, required: true },
      wednesday: { type: String, required: true },
      thursday: { type: String, required: true },
      friday: { type: String, required: true },
      saturday: { type: String, required: true },
      sunday: { type: String, required: true }
    }
  },
  // Theme settings (stored in branding for admin editing)
  theme: {
    colors: {
      primary: { type: String, required: true },
      secondary: { type: String, required: true },
      accent: { type: String, required: true },
      background: { type: String, required: true },
      text: { type: String, required: true },
      textSecondary: { type: String, required: true }
    },
    fonts: {
      primary: { type: String, required: true },
      secondary: { type: String, required: true },
      heading: { type: String, required: true }
    }
  },
  // SEO and site settings (stored in branding for admin editing)
  site: {
    seo: {
      title: { type: String, required: true },
      description: { type: String, required: true },
      keywords: { type: String, required: true }
    },
    features: {
      onlineBooking: { type: Boolean, required: true },
      ecommerce: { type: Boolean, required: true },
      loyaltyProgram: { type: Boolean, required: true },
      giftCards: { type: Boolean, required: true },
      reviews: { type: Boolean, required: true },
      blog: { type: Boolean, required: true }
    }
  }
}, {
  timestamps: true
});

export const Branding = mongoose.model<IBranding>('Branding', brandingSchema);
