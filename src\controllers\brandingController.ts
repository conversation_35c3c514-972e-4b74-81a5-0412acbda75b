import { Request, Response } from 'express';
import { Branding } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';

export class BrandingController {
  /**
   * Get complete branding configuration in a single request
   * This returns ONLY database data - no hardcoded values
   */
  static async getCompleteBranding(req: Request, res: Response): Promise<void> {
    try {
      // Get branding content from database
      let branding = await Branding.findOne();

      if (!branding) {
        // Create default branding if none exists
        branding = await Branding.create({});
      }

      // Return the database branding data in the exact format expected by frontend
      const completeBranding = {
        // Flatten the branding data with all the fields from database
        branding: {
          // Core images and branding
          logo: branding.global?.logo || '',
          heroImage: branding.home?.heroImage || '',
          favicon: branding.global?.logo || '',

          // Home page content
          heroTitle: branding.home?.heroTitle || '',
          heroSubtitle: branding.home?.heroSubtitle || '',
          aboutTitle: branding.home?.aboutTitle || '',
          aboutText: branding.home?.aboutText || '',

          // Services content
          servicesTitle: branding.services?.pageTitle || '',
          servicesSubtitle: branding.services?.pageSubtitle || '',
          servicesDescription: branding.services?.pageDescription || '',
          serviceLocMaintenance: branding.services?.serviceLocMaintenance || '',
          serviceLocMaintenanceDesc: branding.services?.serviceLocMaintenanceDesc || '',
          serviceStarterLocs: branding.services?.serviceStarterLocs || '',
          serviceStarterLocsDesc: branding.services?.serviceStarterLocsDesc || '',
          serviceLocStyling: branding.services?.serviceLocStyling || '',
          serviceLocStylingDesc: branding.services?.serviceLocStylingDesc || '',
          serviceNaturalHairCare: branding.services?.serviceNaturalHairCare || '',
          serviceNaturalHairCareDesc: branding.services?.serviceNaturalHairCareDesc || '',

          // Consultation content
          consultationTitle: branding.consultation?.pageTitle || '',
          consultationSubtitle: branding.consultation?.pageSubtitle || '',
          consultationDescription: branding.consultation?.pageDescription || '',
          consultationFormTitle: branding.consultation?.formTitle || '',
          consultationFormSubtitle: branding.consultation?.formSubtitle || '',

          // Shop content
          shopTitle: branding.shop?.pageTitle || '',
          shopSubtitle: branding.shop?.pageSubtitle || '',
          shopDescription: branding.shop?.pageDescription || '',
          shopFeaturedTitle: branding.shop?.featuredCollectionTitle || '',

          // Dashboard content
          dashboardWelcome: branding.dashboard?.welcomeMessage || '',
          dashboardOverviewTitle: branding.dashboard?.overviewTitle || '',
          dashboardAppointmentsTitle: branding.dashboard?.appointmentsTitle || '',
          dashboardOrdersTitle: branding.dashboard?.ordersTitle || '',
          dashboardFavoritesTitle: branding.dashboard?.favoritesTitle || '',
          dashboardProfileTitle: branding.dashboard?.profileTitle || '',
          dashboardNextAppointment: branding.dashboard?.nextAppointment || '',
          dashboardRecentOrders: branding.dashboard?.recentOrders || '',
          dashboardLoyaltyTitle: branding.dashboard?.loyaltyTitle || '',

          // Authentication content
          loginTitle: branding.login?.pageTitle || '',
          loginSubtitle: branding.login?.pageSubtitle || '',
          signupTitle: branding.signup?.pageTitle || '',
          signupSubtitle: branding.signup?.pageSubtitle || '',

          // Cart content
          cartTitle: branding.cart?.pageTitle || '',
          cartEmptyMessage: branding.cart?.emptyCartMessage || '',
          cartShippingMessage: branding.messages?.cartShipping || '',

          // Buttons
          bookNowButton: branding.buttons?.bookNow || '',
          shopNowButton: branding.buttons?.shopNow || '',
          learnMoreButton: branding.buttons?.learnMore || '',
          viewAllButton: branding.buttons?.viewAll || '',
          continueShoppingButton: branding.buttons?.continueShopping || '',
          proceedToCheckoutButton: branding.buttons?.proceedToCheckout || '',
          addToCartButton: branding.buttons?.addToCart || '',
          scheduleConsultationButton: branding.buttons?.scheduleConsultation || '',
          writeReviewButton: branding.buttons?.writeReview || '',

          // Navigation
          navHome: branding.navigation?.home || '',
          navServices: branding.navigation?.services || '',
          navShop: branding.navigation?.shop || '',
          navConsultation: branding.navigation?.consultation || '',
          navLogin: branding.navigation?.login || '',
          navSignup: branding.navigation?.signup || '',
          navDashboard: branding.navigation?.dashboard || '',

          // Footer
          footerQuickLinks: 'Quick Links',
          footerContact: 'Contact',
          footerFollowUs: 'Follow Us',
          footerCopyright: branding.footer?.copyrightText || '',

          // Testimonials
          testimonialsTitle: branding.testimonials?.title || '',
          testimonialsSubtitle: branding.testimonials?.subtitle || '',
          reviewsTitle: branding.reviews?.title || '',

          // Messages
          loadingMessage: branding.messages?.loading || '',
          errorMessage: branding.messages?.error || '',
          notFoundMessage: branding.messages?.notFound || '',
          comingSoonMessage: branding.messages?.comingSoon || ''
        },

        // Business profile from database
        business: branding.business || {},

        // Theme settings from database
        theme: branding.theme || {},

        // Site settings from database
        site: branding.site || {}
      }

      sendSuccess(res, 'Complete branding configuration retrieved successfully', completeBranding);
    } catch (error) {
      console.error('Get complete branding error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getBrandingContent(req: Request, res: Response): Promise<void> {
    try {
      let branding = await Branding.findOne();

      if (!branding) {
        // Create default branding if none exists
        branding = await Branding.create({});
      }

      sendSuccess(res, 'Branding content retrieved successfully', branding);
    } catch (error) {
      console.error('Get branding content error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getBusinessProfile(req: Request, res: Response): Promise<void> {
    try {
      // Return default business profile
      const businessProfile = {
        name: 'Goldie Locs By Tina',
        tagline: 'By Tina',
        description: 'Professional locs and natural hair care services. Specializing in micro locs, traditional locs, and natural hair maintenance.',
        phone: '(*************',
        email: '<EMAIL>',
        address: {
          street: '123 Beauty Street',
          city: 'Atlanta',
          state: 'GA',
          zip: '30309',
          full: '123 Beauty Street, Atlanta, GA 30309'
        },
        social: {
          instagram: 'https://instagram.com/goldielocs',
          facebook: 'https://facebook.com/goldielocs',
          twitter: 'https://twitter.com/goldielocs'
        },
        hours: {
          monday: '9:00 AM - 6:00 PM',
          tuesday: '9:00 AM - 6:00 PM',
          wednesday: '9:00 AM - 6:00 PM',
          thursday: '9:00 AM - 6:00 PM',
          friday: '9:00 AM - 6:00 PM',
          saturday: '9:00 AM - 4:00 PM',
          sunday: 'Closed'
        }
      }

      sendSuccess(res, 'Business profile retrieved successfully', businessProfile);
    } catch (error) {
      console.error('Error getting business profile:', error);
      sendError(res, 'Failed to get business profile');
    }
  }

  static async getThemeSettings(req: Request, res: Response): Promise<void> {
    try {
      // Return default theme settings
      const themeSettings = {
        colors: {
          primary: '#008000',      // Green
          secondary: '#f3d016',    // Gold/Yellow
          accent: '#006600',       // Dark Green
          background: '#ffffff',   // White
          text: '#000000',         // Black
          textSecondary: '#666666' // Gray
        },
        fonts: {
          primary: 'Inter, system-ui, sans-serif',
          secondary: 'Inter, system-ui, sans-serif',
          heading: 'Inter, system-ui, sans-serif'
        }
      }

      sendSuccess(res, 'Theme settings retrieved successfully', themeSettings);
    } catch (error) {
      console.error('Error getting theme settings:', error);
      sendError(res, 'Failed to get theme settings');
    }
  }

  static async getSiteSettings(req: Request, res: Response): Promise<void> {
    try {
      // Return default site settings
      const siteSettings = {
        seo: {
          title: 'Goldie Locs By Tina - Professional Loc Services',
          description: 'Professional locs and natural hair care services in Atlanta, GA. Specializing in micro locs, traditional locs, and natural hair maintenance.',
          keywords: 'locs, dreadlocks, natural hair, hair care, Atlanta, micro locs, loc maintenance'
        },
        features: {
          onlineBooking: true,
          ecommerce: true,
          loyaltyProgram: true,
          giftCards: true,
          reviews: true,
          blog: false
        }
      }

      sendSuccess(res, 'Site settings retrieved successfully', siteSettings);
    } catch (error) {
      console.error('Error getting site settings:', error);
      sendError(res, 'Failed to get site settings');
    }
  }

  static async updateBrandingContent(req: Request, res: Response): Promise<void> {
    try {
      const updateData = req.body;

      let branding = await Branding.findOne();
      
      if (!branding) {
        branding = await Branding.create(updateData);
      } else {
        Object.assign(branding, updateData);
        await branding.save();
      }

      sendSuccess(res, 'Branding content updated successfully', branding);
    } catch (error) {
      console.error('Update branding content error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateBrandingSection(req: Request, res: Response): Promise<void> {
    try {
      const { section } = req.params;
      const updateData = req.body;

      const validSections = [
        'global', 'home', 'services', 'shop', 'consultation',
        'login', 'signup', 'cart', 'productDetail', 'footer',
        'dashboard', 'buttons', 'navigation', 'testimonials',
        'reviews', 'messages', 'business', 'theme', 'site'
      ];

      if (!validSections.includes(section)) {
        sendError(res, 'Invalid section specified');
        return;
      }

      let branding = await Branding.findOne();
      
      if (!branding) {
        branding = await Branding.create({});
      }

      // Update specific section
      (branding as any)[section] = { ...(branding as any)[section], ...updateData };
      await branding.save();

      sendSuccess(res, `${section} section updated successfully`, branding);
    } catch (error) {
      console.error('Update branding section error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
